console.log('[Preload Script] Executing now for URL:', document.location.href);
const { ipc<PERSON><PERSON><PERSON> } = require('electron');

function injectBlockingCSS() {
    try {
        const style = document.createElement('style');
        style.id = '__earlyImageBlockerStyle'; // Ensure this ID is used
        style.textContent = `
            /* Aggressive CSS for initial image hiding (from preload.js) */
            html body img,
            html body svg,
            html body picture,
            html body video[poster],
            html body input[type="image"],
            html body *[style*="background-image"],
            html body *[style*="background:url"],
            html body .image, html body .img, html body .photo, html body .picture,
            html body [class*="image"], html body [class*="img"], html body [class*="photo"], html body [class*="picture"],
            html body [id*="image"], html body [id*="img"] {
                display: none !important; visibility: hidden !important; opacity: 0 !important;
                background-image: none !important; border: none !important; padding: 0 !important;
                margin: 0 !important; width: 0 !important; height: 0 !important; overflow: hidden !important;
            }
            html body img[loading="lazy"], html body img[data-src], html body img[data-lazy-src] {
                display: none !important; visibility: hidden !important;
            }
            html body iframe[width="1"], html body iframe[height="1"], 
            html body iframe[style*="display: none"], html body iframe[style*="visibility: hidden"] {
                display: none !important; visibility: hidden !important;
            }
        `;
        
        const injectStyleWhenReady = () => {
            if (document.head) {
                const oldStyle = document.getElementById('__earlyImageBlockerStyle');
                if (oldStyle) oldStyle.remove();
                document.head.appendChild(style);
                console.log('[Preload] Injected early image blocking CSS for:', document.location.href);
            } else {
                document.addEventListener('DOMContentLoaded', () => {
                    if (document.head) {
                        const oldStyle = document.getElementById('__earlyImageBlockerStyle');
                        if (oldStyle) oldStyle.remove();
                        document.head.appendChild(style);
                        console.log('[Preload] Injected early image blocking CSS (DOMContentLoaded) for:', document.location.href);
                    }
                }, { once: true });
            }
        };
        injectStyleWhenReady();
    } catch (e) {
        console.error('[Preload] Error injecting early image blocking CSS:', e);
    }
}

(async () => {
    try {
        if (document.location.href && !document.location.href.startsWith('about:') && !document.location.href.startsWith('chrome-error:')) {
            console.log(`[Preload Script] Asking main process if images should be blocked for ${document.location.href}`);
            const shouldBlock = await ipcRenderer.invoke('electronBrowser:should-block-images-for-url', document.location.href);
            console.log(`[Preload Script] Main process response for ${document.location.href}: shouldBlock = ${shouldBlock}`);
            if (shouldBlock) {
                injectBlockingCSS();
            } else {
                console.log(`[Preload Script] Images will NOT be pre-blocked for URL: ${document.location.href}`);
            }
        } else {
            console.log(`[Preload Script] Not querying for image blocking for special URL: ${document.location.href}`);
        }
    } catch (err) {
        console.error('[Preload Script] Error invoking electronBrowser:should-block-images-for-url. Defaulting to NOT injecting CSS from preload. Error:', err);
        // If IPC fails (e.g., handler not set up in main.js), we default to NOT blocking from preload.
        // This means app.html's dom-ready logic will be the sole enforcer, which might cause a flash on non-whitelisted sites.
    }
})();

// Expose API for settings, override, and stats
const { contextBridge } = require('electron');
contextBridge.exposeInMainWorld('electronAPI', {
    setTimeLimit: (domain, minutes) => ipcRenderer.send('set-time-limit', { domain, minutes }),
    saveSettings: (settings) => ipcRenderer.send('save-settings', settings),
    loadSettings: () => ipcRenderer.invoke('load-settings'),
    requestOverride: (domain, minutes) => ipcRenderer.send('override', { domain, minutes }),
    getStats: () => ipcRenderer.invoke('get-stats'),
    onBlockPage: (callback) => ipcRenderer.on('block-page', (event, data) => callback(data)),
});

// Initialize store
let store;
(async () => {
    const Store = (await import('electron-store')).default;
    store = new Store();
})();