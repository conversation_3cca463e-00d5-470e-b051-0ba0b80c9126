// Webview event handling
function setupWebview(webview) {
    webview.addEventListener('dom-ready', () => {
        try {
            const url = webview.getURL();
            const domain = new URL(url).hostname;

            // Get settings from store
            const settings = store.get('settings') || {};
            const blockImages = settings.blockImages === 'true';
            const imageWhitelist = settings.imageWhitelist ? JSON.parse(settings.imageWhitelist) : [];

            // Check if current domain is in whitelist
            const isWhitelisted = imageWhitelist.some(d => domain.includes(d));

            if (blockImages && !isWhitelisted) {
                webview.insertCSS('img, picture, source { display: none !important; }');
            }
        } catch (err) {
            console.error('Error in dom-ready handler:', err);
        }
    });

    webview.addEventListener('did-navigate', (event) => {
        console.log('Navigation occurred:', event.url);
    });

    webview.addEventListener('page-title-updated', (event) => {
        console.log('Page title updated:', event.title);
    });

    webview.addEventListener('did-fail-load', (event) => {
        console.log('Page failed to load:', event.errorCode, event.errorDescription);
    });

    webview.addEventListener('render-process-gone', (event) => {
        console.log('Webview crashed:', event.reason);
    });

    webview.addEventListener('unresponsive', () => {
        console.log('Webview became unresponsive');
    });

    webview.addEventListener('responsive', () => {
        console.log('Webview became responsive again');
    });

    // Set up webview preferences
    webview.setAttribute('webpreferences', 'images=true, javascript=true, webSecurity=true, contextIsolation=true');
    webview.setAttribute('partition', 'persist:main');
    webview.setAttribute('allowpopups', 'true');
    webview.setAttribute('preload', './preload.js');
}

// Function to create a new webview
function createWebview(url, tabId) {
    const webview = document.createElement('webview');
    const webviewContainer = document.getElementById('webview-container');
    const tabsContainer = document.getElementById('tabs');
    const tab = document.createElement('div');
    tab.className = 'tab';
    tab.textContent = 'New Tab';
    tab.dataset.tabId = tabId;

    setupWebview(webview);

    // Set src and style
    webview.src = url;
    webview.style.display = 'none';
    webview.style.width = '100%';
    webview.style.height = '100%';
    webview.style.flex = '1';

    // Add to container
    webviewContainer.appendChild(webview);

    // Add to tabs array
    tabs.push({ id: tabId, webview: webview, element: tab });
    tabsContainer.appendChild(tab);

    // Set as active tab
    setActiveTab(tabId);

    // Show webview
    webview.style.display = 'flex';
}

// Export functions
module.exports = {
    setupWebview,
    createWebview
};