// Video Blocker Module - Clean Foundation
// This module provides basic video blocking functionality that can be easily extended

console.log('VIDEO BLOCKER MODULE: Loaded');

// Function to apply basic video blocking
function blockVideos() {
    console.log('VIDEO BLOCKER: Applying basic video blocking');

    // Remove any existing video blocker styles
    const existingBlocker = document.getElementById('electron-browser-video-blocker');
    if (existingBlocker) {
        existingBlocker.remove();
    }

    // Create basic CSS to hide video elements
    const style = document.createElement('style');
    style.id = 'electron-browser-video-blocker';
    style.textContent = `
        /* Basic video blocking */
        video, audio {
            display: none !important;
            visibility: hidden !important;
        }
    `;

    // Add the style to the document
    document.head.appendChild(style);
    console.log('VIDEO BLOCKER: Added basic video blocking CSS');

    // Basic JavaScript to pause any existing videos
    try {
        const mediaElements = document.querySelectorAll('video, audio');
        console.log('VIDEO BLOCKER: Found', mediaElements.length, 'media elements to pause');

        mediaElements.forEach((media, index) => {
            if (!media.paused) {
                media.pause();
                console.log(`VIDEO BLOCKER: Paused media element ${index}`);
            }
        });
    } catch (e) {
        console.error('VIDEO BLOCKER: Error pausing media elements:', e);
    }

    console.log('VIDEO BLOCKER: Basic video blocking applied');
}

// Function to unblock videos
function unblockVideos() {
    console.log('VIDEO BLOCKER: Removing video blocking');

    // Remove the video blocker style
    const existingBlocker = document.getElementById('electron-browser-video-blocker');
    if (existingBlocker) {
        existingBlocker.remove();
        console.log('VIDEO BLOCKER: Removed video blocking CSS');
    }

    // Add CSS to ensure videos are visible
    const enablerStyle = document.createElement('style');
    enablerStyle.id = 'electron-browser-video-enabler';
    enablerStyle.textContent = `
        /* Ensure videos are visible */
        video, audio {
            display: unset !important;
            visibility: visible !important;
        }
    `;
    document.head.appendChild(enablerStyle);
    console.log('VIDEO BLOCKER: Added video enabling CSS');

    console.log('VIDEO BLOCKER: Video blocking removed');
}

// Function to check if domain is in whitelist
function isDomainWhitelisted(domain, whitelist) {
    if (!Array.isArray(whitelist)) {
        return false;
    }
    
    return whitelist.some(whitelistedDomain => {
        // Simple domain matching - can be enhanced as needed
        return domain.includes(whitelistedDomain) || whitelistedDomain.includes(domain);
    });
}

// Main function to apply video blocking based on settings
function applyVideoBlockingSettings() {
    console.log('VIDEO BLOCKER: Checking video blocking settings');

    try {
        // Get current domain
        const currentDomain = window.location.hostname;
        
        // Get settings from localStorage
        const blockVideosEnabled = localStorage.getItem('blockVideos') === 'true';
        const videoWhitelist = JSON.parse(localStorage.getItem('videoWhitelist') || '[]');
        
        console.log('VIDEO BLOCKER: Domain:', currentDomain);
        console.log('VIDEO BLOCKER: Blocking enabled:', blockVideosEnabled);
        console.log('VIDEO BLOCKER: Whitelist:', videoWhitelist);
        
        // Check if current domain is whitelisted
        const isWhitelisted = isDomainWhitelisted(currentDomain, videoWhitelist);
        console.log('VIDEO BLOCKER: Domain is whitelisted:', isWhitelisted);
        
        if (blockVideosEnabled && !isWhitelisted) {
            blockVideos();
        } else {
            unblockVideos();
        }
    } catch (e) {
        console.error('VIDEO BLOCKER: Error applying settings:', e);
    }
}

// Export functions for use in other modules
if (typeof module !== 'undefined' && module.exports) {
    module.exports = {
        blockVideos,
        unblockVideos,
        isDomainWhitelisted,
        applyVideoBlockingSettings
    };
}

// Auto-apply settings when module loads (for direct script inclusion)
if (typeof window !== 'undefined') {
    // Wait for DOM to be ready
    if (document.readyState === 'loading') {
        document.addEventListener('DOMContentLoaded', applyVideoBlockingSettings);
    } else {
        applyVideoBlockingSettings();
    }
}
