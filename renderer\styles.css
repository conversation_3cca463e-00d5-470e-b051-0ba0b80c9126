* {
  margin: 0;
  padding: 0;
  box-sizing: border-box;
  font-family: -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, Oxygen, Ubuntu, Cantarell, "Open Sans",
    "Helvetica Neue", sans-serif;
}

body,
html {
  height: 100%;
  width: 100%;
  overflow: hidden;
}

.browser {
  display: flex;
  flex-direction: column;
  height: 100vh;
  background-color: #f5f5f5;
}

.browser.dark-mode {
  background-color: #1a1a1a;
  color: #f0f0f0;
}

.titlebar {
  display: flex;
  justify-content: flex-end;
  align-items: center;
  height: 32px;
  background-color: #f0f0f0;
  -webkit-app-region: drag;
}

.dark-mode .titlebar {
  background-color: #2d2d2d;
}

.titlebar-drag-region {
  flex: 1;
  height: 100%;
  -webkit-app-region: drag;
}

.window-controls {
  display: flex;
  -webkit-app-region: no-drag;
}

.window-control {
  width: 46px;
  height: 32px;
  border: none;
  background: transparent;
  color: #666;
  font-size: 14px;
  cursor: pointer;
  display: flex;
  align-items: center;
  justify-content: center;
  transition: background-color 0.2s;
}

.dark-mode .window-control {
  color: #999;
}

.window-control:hover {
  background-color: rgba(0, 0, 0, 0.1);
}

.dark-mode .window-control:hover {
  background-color: rgba(255, 255, 255, 0.1);
}

#titlebar-close:hover {
  background-color: #e81123;
  color: white;
}

.dark-mode #titlebar-close:hover {
  background-color: #e81123;
  color: white;
}

.tabs-container {
  display: flex;
  background-color: #e0e0e0;
  height: 40px;
  padding: 5px 5px 0 5px;
}

.dark-mode .tabs-container {
  background-color: #2a2a2a;
}

.tabs {
  display: flex;
  flex: 1;
  overflow-x: auto;
  white-space: nowrap;
}

.tab {
  display: flex;
  align-items: center;
  min-width: 180px;
  max-width: 200px;
  height: 35px;
  background-color: #d0d0d0;
  margin-right: 2px;
  padding: 0 10px;
  border-radius: 8px 8px 0 0;
  cursor: pointer;
  position: relative;
}

.dark-mode .tab {
  background-color: #3a3a3a;
  color: #e0e0e0;
}

.tab.active {
  background-color: #ffffff;
  color: #000000;
}

.dark-mode .tab.active {
  background-color: #202020;
  color: #ffffff;
}

.tab-title {
  flex: 1;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
  font-size: 14px;
}

.tab-close {
  margin-left: 5px;
  width: 16px;
  height: 16px;
  text-align: center;
  line-height: 16px;
  border-radius: 50%;
}

.tab-close:hover {
  background-color: #ff6b6b;
  color: white;
}

#new-tab-button {
  width: 30px;
  height: 35px;
  background-color: transparent;
  border: none;
  font-size: 20px;
  cursor: pointer;
  color: inherit;
}

.toolbar {
  display: flex;
  align-items: center;
  height: 50px;
  padding: 0 10px;
  background-color: #ffffff;
  border-bottom: 1px solid #e0e0e0;
}

.dark-mode .toolbar {
  background-color: #000000;
  border-bottom: 1px solid #3a3a3a;
}

.toolbar button {
  width: 40px;
  height: 40px;
  margin-right: 5px;
  background-color: transparent;
  border: none;
  font-size: 18px;
  cursor: pointer;
  border-radius: 4px;
  color: inherit;
}

.toolbar button:hover {
  background-color: #f0f0f0;
}

.dark-mode .toolbar button:hover {
  background-color: #3a3a3a;
}

#url-input {
  flex: 1;
  height: 36px;
  padding: 0 10px;
  border: 1px solid #e0e0e0;
  border-radius: 20px;
  font-size: 14px;
  margin: 0 10px;
  background-color: #ffffff;
  color: #000000;
}

.dark-mode #url-input {
  background-color: #333333;
  color: #ffffff;
  border: 1px solid #444444;
}

.webviews-container {
  flex: 1;
  position: relative;
}

webview {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  display: none;
}

webview.active {
  display: flex;
}

.dropdown-menu {
  position: fixed;
  width: 200px;
  background-color: white;
  border: 1px solid #e0e0e0;
  border-radius: 4px;
  box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
  z-index: 1000;
  display: none;
}

.dark-mode .dropdown-menu {
  background-color: #333333;
  border: 1px solid #444444;
  box-shadow: 0 2px 10px rgba(0, 0, 0, 0.3);
}

.menu-item {
  padding: 12px 15px;
  cursor: pointer;
  transition: background-color 0.2s;
  border-bottom: 1px solid #e0e0e0;
}

.menu-item:last-child {
  border-bottom: none;
}

.menu-item:hover {
  background-color: #f5f5f5;
}

.dark-mode .menu-item {
  border-bottom: 1px solid #444444;
}

.dark-mode .menu-item:hover {
  background-color: #444444;
}

.bookmarks-panel {
  position: absolute;
  top: 90px;
  right: 10px;
  width: 300px;
  background-color: white;
  border: 1px solid #e0e0e0;
  border-radius: 4px;
  box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
  z-index: 1000;
  display: none;
}

.dark-mode .bookmarks-panel {
  background-color: #333333;
  border: 1px solid #444444;
  box-shadow: 0 2px 10px rgba(0, 0, 0, 0.3);
}

.panel-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 10px 15px;
  border-bottom: 1px solid #e0e0e0;
}

.dark-mode .panel-header {
  border-bottom: 1px solid #444444;
}

.panel-header button {
  background: none;
  border: none;
  font-size: 18px;
  cursor: pointer;
  color: inherit;
}

#bookmarks-list {
  max-height: 400px;
  overflow-y: auto;
}

.bookmark-item {
  display: flex;
  align-items: center;
  padding: 10px 15px;
  cursor: pointer;
  border-bottom: 1px solid #f0f0f0;
}

.dark-mode .bookmark-item {
  border-bottom: 1px solid #444444;
}

.bookmark-item:hover {
  background-color: #f5f5f5;
}

.dark-mode .bookmark-item:hover {
  background-color: #444444;
}

.bookmark-title {
  flex: 1;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}
