// Store original createElement
const originalCreateElement = document.createElement;

// Function to block images
function blockImages() {
    console.log('IMAGE BLOCKER SCRIPT: Blocking images');

    // Remove any existing image styles first
    const existingBlocker = document.getElementById('electron-browser-image-blocker');
    if (existingBlocker) {
        existingBlocker.remove();
    }

    const existingEnabler = document.getElementById('electron-browser-image-enabler');
    if (existingEnabler) {
        existingEnabler.remove();
    }

    // Create a style element to hide images
    const style = document.createElement('style');
    style.id = 'electron-browser-image-blocker';
    style.textContent = `
        /* Block all images */
        img, svg, picture,
        iframe[src*=".jpg"], iframe[src*=".jpeg"],
        iframe[src*=".png"], iframe[src*=".gif"],
        iframe[src*=".webp"], iframe[src*=".svg"] {
            visibility: hidden !important;
            display: none !important;
            opacity: 0 !important;
            width: 0 !important;
            height: 0 !important;
            position: absolute !important;
            top: -9999px !important;
            left: -9999px !important;
        }

        /* Block background images */
        *[style*="background-image"],
        *[style*="background:url"],
        *[style*="background-url"],
        *[style*="background-image:url"],
        *[style*="background: url"] {
            background-image: none !important;
            background: #f0f0f0 !important;
        }

        /* Block CSS content with images */
        *::before, *::after {
            content: none !important;
        }
    `;

    // Add the style to the document
    document.head.appendChild(style);
    console.log('IMAGE BLOCKER SCRIPT: Added image blocker style');

    // Store original createElement if not already stored
    if (!window._originalCreateElement) {
        window._originalCreateElement = document.createElement;
    }

    // Override createElement to block image loading
    document.createElement = function(tagName) {
        const element = window._originalCreateElement.call(document, tagName);

        if (tagName.toLowerCase() === 'img') {
            console.log('IMAGE BLOCKER SCRIPT: Intercepted image creation');

            // Store original src setter/getter
            const descriptor = Object.getOwnPropertyDescriptor(HTMLImageElement.prototype, 'src');
            if (descriptor) {
                // Override src property
                Object.defineProperty(element, 'src', {
                    set: function(value) {
                        // Store the original src for later use
                        element.setAttribute('data-original-src', value);
                        console.log('IMAGE BLOCKER SCRIPT: Blocked image load:', value);
                        return '';
                    },
                    get: function() {
                        return element.getAttribute('data-original-src') || '';
                    }
                });
            }

            // Also hide the element
            element.style.display = 'none';
            element.style.visibility = 'hidden';
        }

        return element;
    };

    // Also block existing images
    try {
        const images = document.querySelectorAll('img');
        console.log('IMAGE BLOCKER SCRIPT: Found', images.length, 'existing images to block');

        images.forEach((img, index) => {
            // Store the original src
            const src = img.getAttribute('src');
            if (src) {
                console.log(`IMAGE BLOCKER SCRIPT: Blocking existing image ${index}:`, src);
                img.setAttribute('data-original-src', src);
                img.removeAttribute('src');
            }

            // Hide the image
            img.style.display = 'none';
            img.style.visibility = 'hidden';
        });
    } catch (e) {
        console.error('IMAGE BLOCKER SCRIPT: Error blocking existing images:', e);
    }

    console.log('IMAGE BLOCKER SCRIPT: Images blocked');
}

// Function to unblock images
function unblockImages() {
    console.log('IMAGE BLOCKER SCRIPT: Unblocking images');

    // Remove the image blocker style
    const existingBlocker = document.getElementById('electron-browser-image-blocker');
    if (existingBlocker) {
        existingBlocker.remove();
    }

    // Add a style to ensure images are visible
    const enablerStyle = document.createElement('style');
    enablerStyle.id = 'electron-browser-image-enabler';
    enablerStyle.textContent = `
        /* Ensure images are visible */
        img, svg, picture,
        iframe[src*=".jpg"], iframe[src*=".jpeg"],
        iframe[src*=".png"], iframe[src*=".gif"],
        iframe[src*=".webp"], iframe[src*=".svg"] {
            visibility: visible !important;
            display: inline-block !important;
            opacity: 1 !important;
            width: auto !important;
            height: auto !important;
            position: static !important;
        }

        /* Restore background images */
        *[style*="background-image"],
        *[style*="background:url"],
        *[style*="background-url"],
        *[style*="background-image:url"],
        *[style*="background: url"] {
            background-image: initial !important;
            background: initial !important;
        }

        /* Restore CSS content */
        *::before, *::after {
            content: initial !important;
        }
    `;
    document.head.appendChild(enablerStyle);
    console.log('IMAGE BLOCKER SCRIPT: Added image enabler style');

    // Restore original createElement if we have it stored
    if (window._originalCreateElement) {
        document.createElement = window._originalCreateElement;
        console.log('IMAGE BLOCKER SCRIPT: Restored original createElement');
    }

    // Restore existing images
    try {
        const images = document.querySelectorAll('img');
        console.log('IMAGE BLOCKER SCRIPT: Found', images.length, 'images to unblock');

        images.forEach((img, index) => {
            // Restore original src if available
            const originalSrc = img.getAttribute('data-original-src');
            if (originalSrc) {
                console.log(`IMAGE BLOCKER SCRIPT: Unblocking image ${index}:`, originalSrc);
                img.setAttribute('src', originalSrc);
            }

            // Make the image visible
            img.style.display = '';
            img.style.visibility = '';
        });
    } catch (e) {
        console.error('IMAGE BLOCKER SCRIPT: Error unblocking images:', e);
    }

    console.log('IMAGE BLOCKER SCRIPT: Images unblocked');
}

// Export the functions
module.exports = {
    blockImages,
    unblockImages
};