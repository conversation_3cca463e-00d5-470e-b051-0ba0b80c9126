// Reset Video Blocking Settings Script
// This script resets video blocking to a clean state

console.log('Resetting video blocking settings...');

// Reset video blocking to disabled state
localStorage.setItem('blockVideos', 'false');

// Clear video whitelist
localStorage.setItem('videoWhitelist', '[]');

console.log('Video blocking settings have been reset:');
console.log('- Video blocking: disabled');
console.log('- Video whitelist: cleared');
console.log('');
console.log('You can now re-configure video blocking from Settings → Parental Control → Video Blocking');
console.log('The new implementation is minimal and can be easily extended.');
