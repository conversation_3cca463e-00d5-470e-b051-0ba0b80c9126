<!DOCTYPE html>
<html>
<head>
    <title>Page Blocked</title>
    <style>
        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Oxygen, Ubuntu, Cantarell, 'Open Sans', 'Helvetica Neue', sans-serif;
            background-color: #f44336;
            color: #fff;
            margin: 0;
            padding: 0;
            display: flex;
            justify-content: center;
            align-items: center;
            height: 100vh;
            text-align: center;
        }

        .dark-mode {
            background-color: #d32f2f;
        }

        .container {
            max-width: 600px;
            padding: 40px;
            background-color: #fff;
            border-radius: 16px;
            box-shadow: 0 10px 30px rgba(0, 0, 0, 0.2);
            animation: fadeIn 0.5s ease-in-out;
            color: #333;
        }

        .dark-mode .container {
            background-color: #2d2d2d;
            color: #e0e0e0;
            box-shadow: 0 10px 30px rgba(0, 0, 0, 0.4);
        }

        @keyframes fadeIn {
            from { opacity: 0; transform: translateY(-20px); }
            to { opacity: 1; transform: translateY(0); }
        }

        .icon {
            font-size: 64px;
            margin-bottom: 20px;
        }

        h1 {
            font-size: 28px;
            margin-bottom: 20px;
            color: #d32f2f;
        }

        .dark-mode h1 {
            color: #f44336;
        }

        p {
            font-size: 16px;
            line-height: 1.6;
            margin-bottom: 20px;
            color: #555;
        }

        .dark-mode p {
            color: #bbb;
        }

        button {
            background-color: #4285f4;
            color: white;
            border: none;
            padding: 12px 24px;
            font-size: 16px;
            border-radius: 8px;
            cursor: pointer;
            transition: background-color 0.2s;
            margin-top: 20px;
        }

        button:hover {
            background-color: #3367d6;
        }

        .reason {
            background-color: #f5f5f5;
            padding: 15px;
            border-radius: 8px;
            margin: 20px 0;
            font-weight: 500;
            color: #d32f2f;
        }

        .dark-mode .reason {
            background-color: #333;
            color: #f44336;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="icon">⛔</div>
        <h1>Access Blocked</h1>
        <p>This page has been blocked according to your browser settings.</p>

        <div class="reason" id="block-reason">
            This site has been blocked by the domain, URL, or keyword filter.
        </div>

        <p>If you believe this is an error, please check your blocking settings in the Advanced tab.</p>

        <div style="margin-top: 20px;">
            <button onclick="goBack()">Go Back</button>
            <button onclick="openSettings()">Open Settings</button>
            <button onclick="goHome()">Go Home</button>
        </div>
    </div>

    <script>
        // Check if dark mode is enabled in localStorage
        function checkDarkMode() {
            try {
                const settings = JSON.parse(localStorage.getItem('settings') || '{}');
                if (settings.darkMode === 'true' || localStorage.getItem('darkMode') === 'true') {
                    document.body.classList.add('dark-mode');
                }
            } catch (e) {
                console.error('Error checking dark mode:', e);
            }
        }

        // Try to determine why the page was blocked
        function determineBlockReason() {
            try {
                const url = document.referrer || window.location.href;
                const reasonElement = document.getElementById('block-reason');

                // Get block lists from localStorage
                const blockDomains = localStorage.getItem('blockDomains') || '';
                const blockUrls = localStorage.getItem('blockUrls') || '';
                const blockKeywords = localStorage.getItem('blockKeywords') || '';

                console.log('Block domains:', blockDomains);
                console.log('Block URLs:', blockUrls);
                console.log('Block keywords:', blockKeywords);

                // Check if we can determine the reason
                if (blockDomains || blockUrls || blockKeywords) {
                    reasonElement.innerHTML = 'This site has been blocked by:<br>';

                    if (blockDomains) {
                        reasonElement.innerHTML += '• Domain filter<br>';
                    }

                    if (blockUrls) {
                        reasonElement.innerHTML += '• URL filter<br>';
                    }

                    if (blockKeywords) {
                        reasonElement.innerHTML += '• Keyword filter<br>';
                    }
                }

                // Show the blocked URL if available
                if (url && url !== 'about:blank' && !url.includes('block.html')) {
                    reasonElement.innerHTML += '<br>Blocked URL: ' + url;
                }
            } catch (e) {
                console.error('Error determining block reason:', e);
            }
        }

        // Go back to the previous page
        function goBack() {
            window.history.back();
        }

        // Open settings
        function openSettings() {
            try {
                // Try to use Electron IPC if available
                const electron = window.require ? window.require('electron') : null;
                if (electron && electron.ipcRenderer) {
                    electron.ipcRenderer.send('open-settings', 'advanced');
                } else {
                    // Fallback to opening app.html
                    window.location.href = 'app.html';
                }
            } catch (e) {
                console.error('Error opening settings:', e);
                // Fallback to opening app.html
                window.location.href = 'app.html';
            }
        }

        // Go to home page
        function goHome() {
            try {
                window.location.href = 'app.html';
            } catch (e) {
                console.error('Error navigating to home:', e);
            }
        }

        // Initialize
        window.onload = function() {
            console.log('Block page loaded');
            checkDarkMode();
            determineBlockReason();
        };
    </script>
</body>
</html>