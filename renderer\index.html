<!DOCTYPE html>
<html>
<head>
  <meta charset="UTF-8">
  <title>Text-Only Browser</title>
  <link rel="stylesheet" href="styles.css">
</head>
<body>
  <div class="browser">
    <div class="titlebar">
      <div class="titlebar-drag-region"></div>
      <div class="window-controls">
        <button id="titlebar-minimize" class="window-control">&#x2212;</button>
        <button id="titlebar-maximize" class="window-control">&#x2610;</button>
        <button id="titlebar-close" class="window-control">&#x2715;</button>
      </div>
    </div>
    <div class="tabs-container">
      <div class="tabs" id="tabs">
        <!-- Tabs will be added here -->
      </div>
      <button id="new-tab-button">+</button>
    </div>
    
    <div class="toolbar">
      <button id="back-button">←</button>
      <button id="forward-button">→</button>
      <button id="refresh-button">↻</button>
      <input type="text" id="url-input" placeholder="Enter URL or search...">
      <button id="go-button">Go</button>
      <button id="bookmark-button">★</button>
      <button id="menu-button">☰</button>
    </div>
    
    <div class="dropdown-menu" id="main-menu">
      <div class="menu-item" id="show-bookmarks">Bookmarks</div>
      <div class="menu-item" id="clear-data">Clear Cache</div>
      <div class="menu-item" id="toggle-dark-mode">Toggle Dark Mode</div>
      <div class="menu-item" id="about">About Browser</div>
    </div>
    
    <div class="bookmarks-panel" id="bookmarks-panel">
      <div class="panel-header">
        <h3>Bookmarks</h3>
        <button id="close-bookmarks">×</button>
      </div>
      <div id="bookmarks-list">
        <!-- Bookmarks will be added here -->
      </div>
    </div>
    
    <div class="webviews-container" id="webviews-container">
      <!-- Webviews will be added here -->
    </div>
  </div>

  <script src="renderer.js"></script>
</body>
</html>
