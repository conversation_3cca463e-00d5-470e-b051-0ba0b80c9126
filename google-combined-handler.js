
                                // Combined Google handler script

                                // Original preload script
                                const { contextBridge, ipc<PERSON><PERSON><PERSON> } = require('electron');

// Expose API for settings, override, and stats
contextBridge.exposeInMainWorld('electronAPI', {
    setTimeLimit: (domain, minutes) => ipcRenderer.send('set-time-limit', { domain, minutes }),
    saveSettings: (settings) => ipcRenderer.send('save-settings', settings),
    loadSettings: () => ipcRenderer.invoke('load-settings'),
    requestOverride: (domain, minutes) => ipcRenderer.send('override', { domain, minutes }),
    getStats: () => ipcRenderer.invoke('get-stats'),
    onBlockPage: (callback) => ipcRenderer.on('block-page', (event, data) => callback(data)),
});

// Initialize store
let store;
(async () => {
    const Store = (await import('electron-store')).default;
    store = new Store();
})();

// Configure WebView settings
window.addEventListener('DOMContentLoaded', () => {
    const webview = document.querySelector('webview');
    if (webview) {
        // Set initial preferences
        webview.setAttribute('webpreferences', 'imagesEnabled=true');
        webview.setZoomFactor(1);

        // Track time spent on websites
        let startTime = Date.now();
        let currentDomain = '';

        webview.addEventListener('did-start-loading', () => {
            startTime = Date.now();
            currentDomain = new URL(webview.getURL()).hostname;
        });

        webview.addEventListener('did-finish-load', () => {
            const timeSpent = Math.floor((Date.now() - startTime) / 1000); // in seconds
            const timeLimits = store.get('timeLimits');
            const dailyUsage = store.get('dailyUsage');
            const today = new Date().toISOString().split('T')[0];

            // Update daily usage
            if (!dailyUsage[today]) {
                dailyUsage[today] = {};
            }
            if (!dailyUsage[today][currentDomain]) {
                dailyUsage[today][currentDomain] = 0;
            }
            dailyUsage[today][currentDomain] += timeSpent;
            store.set('dailyUsage', dailyUsage);

            // Check time limits
            if (timeLimits[currentDomain]) {
                const limit = timeLimits[currentDomain] * 60; // convert to seconds
                if (dailyUsage[today][currentDomain] > limit) {
                    webview.loadURL('about:blank');
                    webview.executeJavaScript(`
                        document.body.innerHTML = '<div style="text-align: center; padding: 20px;">
                            <h2>Time Limit Reached</h2>
                            <p>You have reached your daily time limit for ${currentDomain}</p>
                        </div>';
                    `);
                }
            }
        });

        // Update time tracking every minute
        setInterval(() => {
            if (currentDomain) {
                const timeSpent = Math.floor((Date.now() - startTime) / 1000);
                const dailyUsage = store.get('dailyUsage');
                const today = new Date().toISOString().split('T')[0];

                if (!dailyUsage[today]) {
                    dailyUsage[today] = {};
                }
                if (!dailyUsage[today][currentDomain]) {
                    dailyUsage[today][currentDomain] = 0;
                }
                dailyUsage[today][currentDomain] += 60; // Add one minute
                store.set('dailyUsage', dailyUsage);
            }
        }, 60000);

        // Basic webview setup
        webview.addEventListener('dom-ready', () => {
            console.log('Webview loaded:', webview.getURL());
        });
    }
});

                                // Consent handler
                                // Google Consent Handler
// This script automatically handles Google consent prompts and preserves consent cookies

// Run as early as possible
(function() {
    console.log('Google Consent Handler loaded');
    
    // Set consent cookies immediately
    setConsentCookies();
    
    // Set up observer to handle consent dialogs
    setupConsentObserver();
    
    // Handle any existing consent dialogs
    setTimeout(handleConsentDialogs, 500);
    setTimeout(handleConsentDialogs, 1500);
    setTimeout(handleConsentDialogs, 3000);
    
    // Function to set consent cookies
    function setConsentCookies() {
        try {
            // Set Google consent cookies with very long expiration
            const expiryDate = new Date();
            expiryDate.setFullYear(expiryDate.getFullYear() + 10); // 10 years in the future
            
            // Different consent cookie formats
            const consentValues = [
                'YES+cb.20220301-11-p0.en+FX+119',
                'YES+cb.20210328-17-p0.en+FX+410',
                'PENDING+946',
                'YES+cb.20210328-17-p0.en-GB+FX+410'
            ];
            
            // Try multiple consent cookie formats
            for (const value of consentValues) {
                document.cookie = `CONSENT=${value}; expires=${expiryDate.toUTCString()}; path=/; domain=.google.com; secure; SameSite=None`;
                document.cookie = `CONSENT=${value}; expires=${expiryDate.toUTCString()}; path=/; domain=.youtube.com; secure; SameSite=None`;
                document.cookie = `CONSENT=${value}; expires=${expiryDate.toUTCString()}; path=/; domain=.google.co.uk; secure; SameSite=None`;
                document.cookie = `CONSENT=${value}; expires=${expiryDate.toUTCString()}; path=/; domain=.google.ca; secure; SameSite=None`;
                document.cookie = `CONSENT=${value}; expires=${expiryDate.toUTCString()}; path=/; domain=.google.fr; secure; SameSite=None`;
            }
            
            // Set SOCS cookie - multiple formats
            const socsValues = [
                'CAESEwgDEgk0ODE3Nzk3MzQ',
                'CAESHAgBEhJnd3NfMjAyMzA2MDUtMF9SQzIaAmVuIAEaBgiA_LyaBg',
                'CAESHAgCEhJnd3NfMjAyMzA2MDUtMF9SQzIaAmVuIAEaBgiA_LyaBg'
            ];
            
            for (const value of socsValues) {
                document.cookie = `SOCS=${value}; expires=${expiryDate.toUTCString()}; path=/; domain=.google.com; secure; SameSite=None`;
                document.cookie = `SOCS=${value}; expires=${expiryDate.toUTCString()}; path=/; domain=.youtube.com; secure; SameSite=None`;
            }
            
            // Set NID cookie with long expiration
            const existingNID = getCookie('NID');
            if (existingNID) {
                document.cookie = `NID=${existingNID}; expires=${expiryDate.toUTCString()}; path=/; domain=.google.com; secure; SameSite=None`;
            }
            
            console.log('Set Google consent cookies with 10-year expiration');
        } catch (e) {
            console.error('Error setting consent cookies:', e);
        }
    }
    
    // Helper function to get a cookie value
    function getCookie(name) {
        const value = `; ${document.cookie}`;
        const parts = value.split(`; ${name}=`);
        if (parts.length === 2) return parts.pop().split(';').shift();
        return null;
    }
    
    // Function to handle consent dialogs
    function handleConsentDialogs() {
        try {
            console.log('Checking for Google consent dialogs');
            
            // Various selectors for consent buttons
            const buttonSelectors = [
                // General consent buttons
                'form[action*="consent"] button',
                'div[role="dialog"] button[jsname]',
                '#introAgreeButton',
                '.consent-bump-dialog button',
                // Specific Google button selectors
                'button[jsname="tJiF1e"]',
                'button[jsname="j7LFlb"]',
                'button[jsname="higCR"]',
                'button[jsname="b3VHJd"]',
                'button[jsname="LgbsSe"]',
                // Aria label selectors
                'button[aria-label*="agree"]',
                'button[aria-label*="Accept"]',
                'button[aria-label*="consent"]',
                // YouTube specific
                'tp-yt-paper-button#button',
                // Specific IDs
                '#zV9nZe',
                '#L2AGLb',
                '#W0wltc'
            ];
            
            // Try each selector
            for (const selector of buttonSelectors) {
                const buttons = document.querySelectorAll(selector);
                for (const button of buttons) {
                    if (button.offsetParent !== null) { // Check if button is visible
                        console.log(`Found consent button with selector ${selector}, clicking it`);
                        button.click();
                        return true;
                    }
                }
            }
            
            // Look for buttons with specific text content
            const textMatches = ['agree', 'accept', 'consent', 'i agree', 'accept all', 'got it', 'ok', 'yes'];
            const allButtons = Array.from(document.querySelectorAll('button'));
            for (const button of allButtons) {
                const text = button.textContent.toLowerCase().trim();
                if (textMatches.some(match => text.includes(match)) && button.offsetParent !== null) {
                    console.log(`Found consent button with text "${button.textContent}", clicking it`);
                    button.click();
                    return true;
                }
            }
            
            return false;
        } catch (e) {
            console.error('Error handling consent dialogs:', e);
            return false;
        }
    }
    
    // Set up observer to detect when consent dialog appears
    function setupConsentObserver() {
        try {
            // Wait for document body
            if (!document.body) {
                window.addEventListener('DOMContentLoaded', setupConsentObserver);
                return;
            }
            
            const observer = new MutationObserver((mutations) => {
                for (const mutation of mutations) {
                    if (mutation.addedNodes.length > 0) {
                        if (handleConsentDialogs()) {
                            // If we found and clicked a button, set cookies again
                            setTimeout(setConsentCookies, 500);
                        }
                    }
                }
            });
            
            observer.observe(document.body, {
                childList: true,
                subtree: true
            });
            
            console.log('Set up mutation observer for consent dialogs');
        } catch (e) {
            console.error('Error setting up consent observer:', e);
        }
    }
})();


                                // Login preserver
                                // Google Login Preserver
// This script ensures Google login state is preserved between sessions

// Run as early as possible
(function() {
    console.log('Google Login Preserver loaded');
    
    // Set up observer to detect login state changes
    setupLoginObserver();
    
    // Preserve authentication cookies
    preserveAuthCookies();
    
    // Check for login state periodically
    setTimeout(checkLoginState, 1000);
    setTimeout(checkLoginState, 3000);
    setTimeout(checkLoginState, 5000);
    
    // Function to preserve authentication cookies
    function preserveAuthCookies() {
        try {
            // Set very long expiration for all cookies
            const expiryDate = new Date();
            expiryDate.setFullYear(expiryDate.getFullYear() + 10); // 10 years in the future
            
            // Get all cookies
            const cookies = document.cookie.split(';');
            
            // Important Google authentication cookies to preserve
            const authCookies = [
                'SID', 'HSID', 'SSID', 'APISID', 'SAPISID', 'NID', 
                '__Secure-1PSID', '__Secure-3PSID', '__Secure-1PAPISID', 
                '__Secure-3PAPISID', 'LSID', 'ACCOUNT_CHOOSER', 'GAPS',
                'LSOLH', 'OTZ', 'AEC', '__Host-GAPS', 'OSID', 'OGP', 
                'OGPC', 'DV', '1P_JAR', 'SIDCC', '__Secure-1PSIDCC',
                '__Secure-3PSIDCC', 'SEARCH_SAMESITE', 'UULE', 'CONSENT',
                'SOCS', 'SNID', 'ANID', 'ENID', 'PREF', 'LOGIN_INFO'
            ];
            
            // Check each cookie
            for (const cookie of cookies) {
                const parts = cookie.split('=');
                const name = parts[0].trim();
                const value = parts.slice(1).join('=');
                
                // Check if this is an authentication cookie
                const isAuthCookie = authCookies.some(authCookie => 
                    name === authCookie || name.startsWith('__Secure-') || name.startsWith('__Host-')
                );
                
                if (isAuthCookie && value) {
                    console.log(`Preserving authentication cookie: ${name}`);
                    
                    // Set cookie with long expiration for various domains
                    const domains = ['.google.com', '.youtube.com', '.accounts.google.com'];
                    
                    for (const domain of domains) {
                        try {
                            document.cookie = `${name}=${value}; expires=${expiryDate.toUTCString()}; path=/; domain=${domain}; secure; SameSite=None`;
                        } catch (e) {
                            // Ignore errors for specific domains
                        }
                    }
                }
            }
            
            console.log('Preserved Google authentication cookies with 10-year expiration');
        } catch (e) {
            console.error('Error preserving authentication cookies:', e);
        }
    }
    
    // Function to check login state
    function checkLoginState() {
        try {
            // Check for elements that indicate logged-in state
            const loggedInIndicators = [
                // Profile picture/avatar
                'img[alt="Profile picture"]',
                'img[data-name="Profile picture"]',
                'a[href^="https://accounts.google.com/SignOutOptions"]',
                'a[aria-label*="Google Account"]',
                'a[aria-label*="profile"]',
                'div[data-ogsr-up]',
                'div[data-ogsr-fb]',
                'div[jscontroller="GfN5Qc"]',
                // YouTube specific
                'button[aria-label="Avatar image"]',
                'img[alt="Avatar image"]',
                'yt-img-shadow',
                // Gmail specific
                'div[aria-label*="Account"]',
                'div[data-tooltip="Account"]',
                'a[href*="accounts.google.com/SignOutOptions"]'
            ];
            
            let isLoggedIn = false;
            
            // Check each indicator
            for (const selector of loggedInIndicators) {
                const elements = document.querySelectorAll(selector);
                if (elements.length > 0) {
                    for (const element of elements) {
                        if (element.offsetParent !== null) { // Check if element is visible
                            console.log(`Detected logged-in state with selector: ${selector}`);
                            isLoggedIn = true;
                            break;
                        }
                    }
                    if (isLoggedIn) break;
                }
            }
            
            if (isLoggedIn) {
                console.log('User is logged in, preserving authentication state');
                preserveAuthCookies();
                
                // Store login state in localStorage
                try {
                    localStorage.setItem('googleLoggedIn', 'true');
                    localStorage.setItem('googleLoginTimestamp', Date.now().toString());
                } catch (e) {
                    console.error('Error storing login state in localStorage:', e);
                }
            } else {
                console.log('User is not logged in or login state could not be detected');
            }
        } catch (e) {
            console.error('Error checking login state:', e);
        }
    }
    
    // Set up observer to detect login state changes
    function setupLoginObserver() {
        try {
            // Wait for document body
            if (!document.body) {
                window.addEventListener('DOMContentLoaded', setupLoginObserver);
                return;
            }
            
            const observer = new MutationObserver((mutations) => {
                for (const mutation of mutations) {
                    if (mutation.addedNodes.length > 0) {
                        // Check if any added nodes might indicate login state
                        const addedElements = Array.from(mutation.addedNodes)
                            .filter(node => node.nodeType === Node.ELEMENT_NODE);
                            
                        if (addedElements.length > 0) {
                            // Check login state when DOM changes significantly
                            checkLoginState();
                        }
                    }
                }
            });
            
            observer.observe(document.body, {
                childList: true,
                subtree: true,
                attributes: true,
                attributeFilter: ['src', 'href', 'aria-label']
            });
            
            console.log('Set up mutation observer for login state changes');
        } catch (e) {
            console.error('Error setting up login observer:', e);
        }
    }
    
    // Handle login form submission
    function handleLoginForm() {
        try {
            // Find login forms
            const loginForms = document.querySelectorAll('form[action*="signin"], form[action*="login"], form[action*="accounts.google"]');
            
            for (const form of loginForms) {
                // Add submit event listener
                form.addEventListener('submit', () => {
                    console.log('Login form submitted, will preserve cookies after delay');
                    
                    // Preserve cookies after a delay to allow login to complete
                    setTimeout(preserveAuthCookies, 2000);
                    setTimeout(preserveAuthCookies, 5000);
                    setTimeout(checkLoginState, 3000);
                    setTimeout(checkLoginState, 6000);
                });
                
                console.log('Added submit listener to login form');
            }
        } catch (e) {
            console.error('Error handling login form:', e);
        }
    }
    
    // Set up login form handler
    setTimeout(handleLoginForm, 1000);
    setTimeout(handleLoginForm, 3000);
})();

                            