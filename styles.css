:root {
    /* Light mode colors */
    --bg-color: #ffffff;
    --text-color: #333333;
    --toolbar-bg: #f0f0f0;
    --toolbar-border: #e0e0e0;
    --tab-bg: #e9e9e9;
    --tab-active-bg: #ffffff;
    --tab-hover-bg: #f5f5f5;
    --tab-border: #d0d0d0;
    --tab-active-border: #4285f4;
    --url-bar-bg: #ffffff;
    --url-bar-border: #e0e0e0;
    --url-bar-focus-border: #4285f4;
    --button-bg: #4285f4;
    --button-hover-bg: #3367d6;
    --button-text: #ffffff;
    --webview-bg: #ffffff;
    --divider-color: #e0e0e0;
    --shadow-color: rgba(0, 0, 0, 0.1);
    --modal-bg: #ffffff;
    --modal-overlay: rgba(0, 0, 0, 0.5);
    --titlebar-bg: #f0f0f0;
    --titlebar-text: #333333;
    --titlebar-button-hover: #e0e0e0;
    --titlebar-button-active: #d0d0d0;
    --titlebar-close-hover: #e81123;
    --titlebar-close-active: #f1707a;

    /* Font settings */
    font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Oxygen, Ubuntu, Cantarell, 'Open Sans', 'Helvetica Neue', sans-serif;
    font-size: 14px;
}

/* Dark mode colors */
body.dark-mode {
    --bg-color: #1e1e1e;
    --text-color: #e0e0e0;
    --toolbar-bg: #2d2d2d;
    --toolbar-border: #3d3d3d;
    --tab-bg: #252525;
    --tab-active-bg: #333333;
    --tab-hover-bg: #3a3a3a;
    --tab-border: #3d3d3d;
    --tab-active-border: #4285f4;
    --url-bar-bg: #333333;
    --url-bar-border: #444444;
    --url-bar-focus-border: #4285f4;
    --button-bg: #4285f4;
    --button-hover-bg: #3367d6;
    --button-text: #ffffff;
    --webview-bg: #1e1e1e;
    --divider-color: #3d3d3d;
    --shadow-color: rgba(0, 0, 0, 0.3);
    --modal-bg: #23272b;
    --modal-overlay: rgba(0, 0, 0, 0.7);
    --titlebar-bg: #000000;
    --titlebar-text: #ffffff;
    --titlebar-button-hover: #333333;
    --titlebar-button-active: #444444;
    --titlebar-close-hover: #e81123;
    --titlebar-close-active: #f1707a;
}

html, body {
    margin: 0;
    padding: 0;
    background: var(--bg-color);
    color: var(--text-color);
    width: 100%;
    height: 100vh;
    overflow: hidden;
    transition: background-color 0.3s ease, color 0.3s ease;
}

body {
    display: flex;
    flex-direction: column;
    min-height: 0;
}

#titlebar {
    display: flex;
    align-items: center;
    height: 32px;
    background: var(--titlebar-bg);
    color: var(--titlebar-text);
    -webkit-app-region: drag;
    user-select: none;
}

#titlebar-menu {
    display: flex;
    align-items: center;
    margin-left: 10px;
}

.menu-item {
    position: relative;
    padding: 0 10px;
    height: 30px;
    display: flex;
    align-items: center;
    cursor: pointer;
    color: #fff;
}

.menu-item:hover {
    background-color: rgba(255, 255, 255, 0.1);
}

.dropdown-content {
    display: none;
    position: absolute;
    top: 100%;
    left: 0;
    background-color: #2d2d2d;
    min-width: 160px;
    box-shadow: 0 8px 16px rgba(0,0,0,0.2);
    z-index: 1000;
}

.menu-item:hover .dropdown-content {
    display: block;
}

.dropdown-content a {
    color: #fff;
    padding: 8px 16px;
    text-decoration: none;
    display: block;
    font-size: 13px;
}

.dropdown-content a:hover {
    background-color: rgba(255, 255, 255, 0.1);
}

.dropdown-content hr {
    border: none;
    border-top: 1px solid #444;
    margin: 4px 0;
}

#titlebar-title {
    flex: 1;
    text-align: center;
    font-size: 12px;
    margin-left: 12px;
}

#titlebar-controls {
    display: flex;
    align-items: center;
    -webkit-app-region: no-drag;
}

.titlebar-button {
    width: 46px;
    height: 32px;
    display: flex;
    align-items: center;
    justify-content: center;
    -webkit-app-region: no-drag;
    cursor: pointer;
    transition: background-color 0.2s;
}

.titlebar-button:hover {
    background-color: var(--titlebar-button-hover);
}

.titlebar-button:active {
    background-color: var(--titlebar-button-active);
}

#titlebar-close:hover {
    background-color: var(--titlebar-close-hover);
}

#titlebar-close:active {
    background-color: var(--titlebar-close-active);
}

#toolbar {
    background: var(--toolbar-bg);
    padding: 8px 12px;
    display: flex;
    gap: 8px;
    align-items: center;
    border-bottom: 1px solid var(--toolbar-border);
    box-shadow: 0 1px 3px var(--shadow-color);
    z-index: 10;
    transition: background-color 0.3s ease, border-color 0.3s ease;
}

#url-input {
    flex: 1;
    padding: 8px 12px;
    border: 1px solid var(--url-bar-border);
    border-radius: 20px;
    background: var(--url-bar-bg);
    color: var(--text-color);
    font-size: 14px;
    outline: none;
    transition: all 0.2s ease;
    box-shadow: 0 1px 2px var(--shadow-color);
}

#url-input:focus {
    border-color: var(--url-bar-focus-border);
    box-shadow: 0 0 0 2px rgba(66, 133, 244, 0.2);
}

button {
    padding: 8px;
    background: var(--button-bg);
    border: none;
    border-radius: 50%;
    color: var(--button-text);
    cursor: pointer;
    width: 32px;
    height: 32px;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 14px;
    transition: all 0.2s ease;
    box-shadow: 0 1px 2px var(--shadow-color);
}

button:hover {
    background: var(--button-hover-bg);
    transform: translateY(-1px);
    box-shadow: 0 2px 4px var(--shadow-color);
}

#go {
    border-radius: 20px;
    width: auto;
    padding: 8px 16px;
}

#tabs {
    display: flex;
    padding: 0 8px;
    background: var(--toolbar-bg);
    height: 36px;
    align-items: flex-end;
    gap: 2px;
    transition: background-color 0.3s ease;
}

.tab {
    padding: 8px 12px;
    background: var(--tab-bg);
    border-radius: 8px 8px 0 0;
    cursor: pointer;
    font-size: 12px;
    max-width: 160px;
    height: 28px;
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
    display: flex;
    align-items: center;
    color: var(--text-color);
    border: 1px solid var(--tab-border);
    border-bottom: none;
    transition: all 0.2s ease;
    position: relative;
    margin-right: 1px;
}

.tab:hover {
    background: var(--tab-hover-bg);
}

.tab.active {
    background: var(--tab-active-bg);
    height: 32px;
    z-index: 1;
    border-top: 2px solid var(--tab-active-border);
    font-weight: 500;
}

#bookmarks {
    display: flex;
    gap: 5px;
    padding: 8px;
    background: #f8f8f8;
    border-bottom: 1px solid #ddd;
    flex-wrap: wrap;
    align-items: center;
    min-height: 20px;
}

#bookmarks:empty,
#bookmarks:has(> span#bookmark-title:only-child) {
    display: none;
}

.bookmark {
    padding: 6px 12px;
    background: #e8e8e8;
    border-radius: 4px;
    cursor: pointer;
    font-size: 12px;
    display: flex;
    align-items: center;
    transition: background-color 0.2s;
}

.bookmark:hover {
    background: #d8d8d8;
}

.bookmark .favicon {
    width: 16px;
    height: 16px;
    margin-right: 6px;
}

.bookmark .remove {
    margin-left: 6px;
    font-size: 14px;
    opacity: 0.5;
    transition: opacity 0.2s;
}

.bookmark .remove:hover {
    opacity: 1;
}

#bookmark-title {
    font-weight: bold;
    margin-right: 10px;
    color: #555;
}

#webview-container {
    flex: 1;
    position: relative;
    background: var(--webview-bg);
    display: flex;
    flex-direction: column;
    min-height: 0;
    transition: background-color 0.3s ease;
}

webview {
    width: 100%;
    height: 100%;
    border: none;
    flex: 1;
    display: flex;
    min-height: 0;
    background: var(--webview-bg);
    transition: background-color 0.3s ease;
}

/* Settings Modal Styles */
.settings-tab {
    padding: 12px 20px;
    cursor: pointer;
    transition: all 0.2s ease;
    color: var(--text-color);
    position: relative;
}

.settings-tab:hover {
    background-color: var(--tab-hover-bg);
}

.settings-tab.active {
    background-color: var(--tab-active-bg);
    border-bottom: 2px solid var(--tab-active-border);
    font-weight: 500;
}

.bookmark-item {
    display: flex;
    align-items: center;
    padding: 10px;
    border-bottom: 1px solid #eee;
}

.bookmark-item:hover {
    background-color: #f5f5f5;
}

.bookmark-favicon {
    width: 16px;
    height: 16px;
    margin-right: 10px;
}

.bookmark-title {
    flex: 1;
    font-weight: 500;
}

.bookmark-url {
    flex: 2;
    color: #666;
    margin: 0 10px;
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
}

.bookmark-actions {
    display: flex;
    gap: 5px;
}

.bookmark-actions button {
    padding: 4px 8px;
    background: #f0f0f0;
    border: 1px solid #ddd;
    border-radius: 4px;
    cursor: pointer;
}

.bookmark-actions button:hover {
    background: #e0e0e0;
}

/* Timer Display */
#timer-display {
    position: fixed;
    top: 5px;
    left: 5px;
    background: rgba(0, 0, 0, 0.7);
    color: white;
    padding: 5px 10px;
    border-radius: 15px;
    font-size: 12px;
    z-index: 1001;
    display: flex;
    align-items: center;
    gap: 5px;
    cursor: pointer;
    transition: background-color 0.2s;
}

#timer-display:hover {
    background: rgba(0, 0, 0, 0.9);
}

#timer-icon {
    font-size: 14px;
}

#timer-text {
    font-family: monospace;
}

/* Dark mode styles */
body.dark-mode #settings-modal > div {
    background: #23272b !important;
    color: #e0e0e0 !important;
}

body.dark-mode .settings-tab {
    color: #e0e0e0 !important;
}

body.dark-mode .settings-tab.active {
    background-color: #23272b !important;
    border-bottom: 2px solid #4285f4 !important;
}

body.dark-mode .settings-panel {
    background: transparent !important;
    color: #e0e0e0 !important;
}

body.dark-mode .settings-panel h2,
body.dark-mode .settings-panel h3,
body.dark-mode .settings-panel h4 {
    color: #e0e0e0 !important;
}

body.dark-mode .settings-panel label,
body.dark-mode .settings-panel p,
body.dark-mode .settings-panel span {
    color: #cccccc !important;
}

body.dark-mode .settings-panel input[type="text"],
body.dark-mode .settings-panel input[type="number"],
body.dark-mode .settings-panel input[type="password"] {
    background: #23272b !important;
    color: #e0e0e0 !important;
    border: 1px solid #444 !important;
}

body.dark-mode .settings-panel input[type="checkbox"] {
    accent-color: #4285f4;
}

body.dark-mode .settings-panel button {
    background: #4285f4 !important;
    color: #fff !important;
    border: none !important;
}

body.dark-mode .settings-panel button:hover {
    background: #3367d6 !important;
}

body.dark-mode .settings-panel .whitelist-item {
    background: #23272b !important;
    color: #e0e0e0 !important;
    border: 1px solid #444 !important;
}

body.dark-mode .settings-panel .whitelist-item:hover {
    background: #2d2d2d !important;
}

body.dark-mode .settings-panel .bookmark-item {
    background: #23272b !important;
    color: #e0e0e0 !important;
    border-bottom: 1px solid #444 !important;
}

body.dark-mode .settings-panel .bookmark-item:hover {
    background: #2d2d2d !important;
}

body.dark-mode .settings-panel .bookmark-actions button {
    background: #333 !important;
    color: #fff !important;
    border: 1px solid #444 !important;
}

body.dark-mode .settings-panel .bookmark-actions button:hover {
    background: #4285f4 !important;
}

/* Feature boxes in settings modal */
body.dark-mode .settings-panel > div[style*="background:#f5f5f5"],
body.dark-mode .settings-panel > div[style*="background: #f5f5f5"],
body.dark-mode .settings-panel > div[style*="background: #fff3cd"],
body.dark-mode .settings-panel > div[style*="background:#fff3cd"] {
    background: #23272b !important;
    color: #e0e0e0 !important;
    border: 1px solid #444 !important;
}

/* Predefined limits boxes */
body.dark-mode .settings-panel > div > div[style*="background:#f5f5f5"],
body.dark-mode .settings-panel > div > div[style*="background: #f5f5f5"] {
    background: #23272b !important;
    color: #e0e0e0 !important;
    border: 1px solid #444 !important;
} 