const { ipc<PERSON><PERSON><PERSON> } = require("electron")

// DOM Elements
const tabsContainer = document.getElementById("tabs")
const newTabButton = document.getElementById("new-tab-button")
const backButton = document.getElementById("back-button")
const forwardButton = document.getElementById("forward-button")
const refreshButton = document.getElementById("refresh-button")
const urlInput = document.getElementById("url-input")
const goButton = document.getElementById("go-button")
const bookmarkButton = document.getElementById("bookmark-button")
const menuButton = document.getElementById("menu-button")
const mainMenu = document.getElementById("main-menu")
const showBookmarksButton = document.getElementById("show-bookmarks")
const clearDataButton = document.getElementById("clear-data")
const toggleDarkModeButton = document.getElementById("toggle-dark-mode")
const aboutButton = document.getElementById("about")
const bookmarksPanel = document.getElementById("bookmarks-panel")
const closeBookmarksButton = document.getElementById("close-bookmarks")
const bookmarksList = document.getElementById("bookmarks-list")
const webviewsContainer = document.getElementById("webviews-container")
const browserContainer = document.querySelector(".browser")
const minimizeButton = document.getElementById("titlebar-minimize")
const maximizeButton = document.getElementById("titlebar-maximize")
const closeButton = document.getElementById("titlebar-close")

// State
const tabs = []
let activeTabId = null
const bookmarks = []
let isDarkMode = false

// Initialize
function init() {
  createNewTab()

  // Event listeners
  newTabButton.addEventListener("click", () => createNewTab())
  backButton.addEventListener("click", navigateBack)
  forwardButton.addEventListener("click", navigateForward)
  refreshButton.addEventListener("click", refreshPage)
  goButton.addEventListener("click", navigateToUrl)
  urlInput.addEventListener("keypress", (e) => {
    if (e.key === "Enter") navigateToUrl()
  })

  bookmarkButton.addEventListener("click", addBookmark)
  menuButton.addEventListener("click", toggleMenu)
  showBookmarksButton.addEventListener("click", showBookmarks)
  clearDataButton.addEventListener("click", clearBrowsingData)
  toggleDarkModeButton.addEventListener("click", toggleDarkMode)
  aboutButton.addEventListener("click", showAbout)
  closeBookmarksButton.addEventListener("click", hideBookmarks)

  // Window control buttons
  minimizeButton.addEventListener("click", () => {
    require('electron').remote.getCurrentWindow().minimize()
  })

  maximizeButton.addEventListener("click", () => {
    const win = require('electron').remote.getCurrentWindow()
    if (win.isMaximized()) {
      win.unmaximize()
    } else {
      win.maximize()
    }
  })

  closeButton.addEventListener("click", () => {
    require('electron').remote.getCurrentWindow().close()
  })

  // Close menu when clicking outside
  document.addEventListener('click', (e) => {
    if (!menuButton.contains(e.target) && !mainMenu.contains(e.target)) {
      mainMenu.style.display = 'none';
    }
  });

  // Keyboard shortcuts
  document.addEventListener("keydown", handleKeyboardShortcuts)

  // IPC listeners
  ipcRenderer.on("navigate-to", (event, url) => {
    const webview = getActiveWebview()
    if (webview) webview.loadURL(url)
  })

  ipcRenderer.on("create-new-tab", (event, url) => {
    createNewTab(url)
  })

  ipcRenderer.on("bookmark-added", (event, bookmark) => {
    bookmarks.push(bookmark)
    renderBookmarks()
  })

  ipcRenderer.on("data-cleared", () => {
    alert("Cache cleared")
    mainMenu.style.display = "none"
  })
}

// Tab Management
function createNewTab(url = "https://www.google.com") {
  const tabId = "tab-" + Date.now()

  // Create tab element
  const tab = document.createElement("div")
  tab.className = "tab"
  tab.id = tabId
  tab.innerHTML = `
    <div class="tab-title">New Tab</div>
    <div class="tab-close">×</div>
  `

  tab.addEventListener("click", () => activateTab(tabId))
  tab.querySelector(".tab-close").addEventListener("click", (e) => {
    e.stopPropagation()
    closeTab(tabId)
  })

  tabsContainer.appendChild(tab)

  // Create webview with settings to disable images and videos
  const webview = document.createElement("webview")
  webview.id = "webview-" + tabId
  webview.setAttribute("src", url)
  webview.setAttribute("autosize", "on")

  // Enable JavaScript and other web features
  webview.setAttribute("allowpopups", "true")

  // Set user agent to indicate text-only browser
  webview.setAttribute("useragent", "TextOnlyBrowser/1.0")

  // Add preload script to disable images and videos in the page
  webview.setAttribute("preload", "./preload.js")

  webviewsContainer.appendChild(webview)

  // Set up webview events
  webview.addEventListener("did-start-loading", () => {
    refreshButton.textContent = "✕"
    const tab = document.getElementById(tabId)
    if (tab) {
      const tabTitle = tab.querySelector(".tab-title")
      tabTitle.textContent = "Loading..."
    }
  })

  webview.addEventListener("did-stop-loading", () => {
    refreshButton.textContent = "↻"
    urlInput.value = webview.getURL()

    // Update tab title
    webview.executeJavaScript("document.title", (title) => {
      const tab = document.getElementById(tabId)
      if (tab) {
        const tabTitle = tab.querySelector(".tab-title")
        tabTitle.textContent = title || "New Tab"
      }
    })
  })

  webview.addEventListener("page-title-updated", (e) => {
    const tab = document.getElementById(tabId)
    if (tab) {
      const tabTitle = tab.querySelector(".tab-title")
      tabTitle.textContent = e.title || "New Tab"
    }
  })

  webview.addEventListener("new-window", (e) => {
    // Handle new window requests by creating a new tab
    createNewTab(e.url)
  })

  // Add to tabs array
  tabs.push({ id: tabId, webviewId: webview.id })

  // Activate the new tab
  activateTab(tabId)
}

function activateTab(tabId) {
  // Deactivate all tabs
  document.querySelectorAll(".tab").forEach((tab) => {
    tab.classList.remove("active")
  })

  document.querySelectorAll("webview").forEach((webview) => {
    webview.classList.remove("active")
  })

  // Activate selected tab
  const tab = document.getElementById(tabId)
  tab.classList.add("active")

  const tabInfo = tabs.find((t) => t.id === tabId)
  if (tabInfo) {
    const webview = document.getElementById(tabInfo.webviewId)
    webview.classList.add("active")

    // Update URL input
    urlInput.value = webview.getURL()

    // Update active tab ID
    activeTabId = tabId
  }
}

function closeTab(tabId) {
  const tabIndex = tabs.findIndex((t) => t.id === tabId)
  if (tabIndex === -1) return

  // Remove tab and webview elements
  const tabInfo = tabs[tabIndex]
  const tab = document.getElementById(tabId)
  const webview = document.getElementById(tabInfo.webviewId)

  tab.remove()
  webview.remove()

  // Remove from tabs array
  tabs.splice(tabIndex, 1)

  // If we closed the active tab, activate another one
  if (activeTabId === tabId && tabs.length > 0) {
    activateTab(tabs[Math.min(tabIndex, tabs.length - 1)].id)
  } else if (tabs.length === 0) {
    // If no tabs left, create a new one
    createNewTab()
  }
}

// Navigation
function getActiveWebview() {
  const tabInfo = tabs.find((t) => t.id === activeTabId)
  return tabInfo ? document.getElementById(tabInfo.webviewId) : null
}

function navigateToUrl() {
  let url = urlInput.value.trim()

  // Add http:// if no protocol specified
  if (!/^https?:\/\//i.test(url) && url !== "") {
    // Check if it's a valid domain or IP
    if (
      /^(([a-zA-Z0-9]|[a-zA-Z0-9][a-zA-Z0-9-]*[a-zA-Z0-9])\.)+([A-Za-z0-9]|[A-Za-z0-9][A-Za-z0-9-]*[A-Za-z0-9])$/.test(
        url,
      )
    ) {
      url = "http://" + url
    } else {
      // Treat as a search query
      url = "https://www.google.com/search?q=" + encodeURIComponent(url)
    }
  }

  const webview = getActiveWebview()
  if (webview) {
    webview.loadURL(url)
  }
}

function navigateBack() {
  const webview = getActiveWebview()
  if (webview && webview.canGoBack()) {
    webview.goBack()
  }
}

function navigateForward() {
  const webview = getActiveWebview()
  if (webview && webview.canGoForward()) {
    webview.goForward()
  }
}

function refreshPage() {
  const webview = getActiveWebview()
  if (webview) {
    if (webview.isLoading()) {
      webview.stop()
    } else {
      webview.reload()
    }
  }
}

// Bookmarks
function addBookmark() {
  const webview = getActiveWebview()
  if (!webview) return

  webview.executeJavaScript("document.title", (title) => {
    const url = webview.getURL()
    const bookmark = { title: title || url, url: url }

    // Check if bookmark already exists
    if (!bookmarks.some((b) => b.url === url)) {
      ipcRenderer.send("add-bookmark", bookmark)
    }
  })
}

function renderBookmarks() {
  bookmarksList.innerHTML = ""

  bookmarks.forEach((bookmark) => {
    const bookmarkItem = document.createElement("div")
    bookmarkItem.className = "bookmark-item"
    bookmarkItem.innerHTML = `
      <div class="bookmark-title">${bookmark.title}</div>
    `

    bookmarkItem.addEventListener("click", () => {
      const webview = getActiveWebview()
      if (webview) {
        webview.loadURL(bookmark.url)
        hideBookmarks()
      }
    })

    bookmarksList.appendChild(bookmarkItem)
  })
}

// UI Controls
function toggleMenu() {
  if (mainMenu.style.display === "block") {
    mainMenu.style.display = "none"
  } else {
    mainMenu.style.display = "block"
    bookmarksPanel.style.display = "none"
    
    // Position the menu below the menu button
    const menuButtonRect = menuButton.getBoundingClientRect()
    mainMenu.style.top = `${menuButtonRect.bottom + 5}px`
    mainMenu.style.right = '10px'
  }
}

function showBookmarks() {
  mainMenu.style.display = "none"
  bookmarksPanel.style.display = "block"
  renderBookmarks()
}

function hideBookmarks() {
  bookmarksPanel.style.display = "none"
}

function clearBrowsingData() {
  ipcRenderer.send("clear-data")
}

function toggleDarkMode() {
  isDarkMode = !isDarkMode
  if (isDarkMode) {
    browserContainer.classList.add("dark-mode")
  } else {
    browserContainer.classList.remove("dark-mode")
  }
  mainMenu.style.display = "none"
}

function showAbout() {
  alert(
    "Text-Only Browser\nVersion 1.0\n\nA browser that blocks images and videos for distraction-free browsing.\n\nKeyboard Shortcuts:\nCtrl+T: New Tab\nCtrl+W: Close Tab\nCtrl+R: Refresh\nCtrl+L: Focus URL Bar",
  )
  mainMenu.style.display = "none"
}

// Keyboard shortcuts
function handleKeyboardShortcuts(e) {
  // Ctrl+T: New Tab
  if (e.ctrlKey && e.key === "t") {
    e.preventDefault()
    createNewTab()
  }

  // Ctrl+W: Close Tab
  if (e.ctrlKey && e.key === "w") {
    e.preventDefault()
    if (activeTabId) {
      closeTab(activeTabId)
    }
  }

  // Ctrl+R: Refresh
  if (e.ctrlKey && e.key === "r") {
    e.preventDefault()
    refreshPage()
  }

  // Ctrl+L: Focus URL Bar
  if (e.ctrlKey && e.key === "l") {
    e.preventDefault()
    urlInput.focus()
    urlInput.select()
  }
}

// Initialize the app
init()
